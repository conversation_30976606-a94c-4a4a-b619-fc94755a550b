sudo apt-get update
sudo apt upgrade -y
sudo apt install python3-pip python3-dev
sudo apt install python3-virtualenv
sudo apt-get install ffmpeg
# sudo pip3 install virtualenv

mkdir ~/vido_ht

cd ~/vido_ht
# virtualenv .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-face-detection.txt

python3 main.py
or
.venv/bin/python main.py


source .venv/bin/activate && python tests/test_temporal_face_tracking_clips.py --detection-interval 3.0 --clip-id clip_013 --preview-only

# Test the intelligent highlights system
source .venv/bin/activate && python test_intelligent_highlights.py ttests/sample/video1/video1.mp4 "AI,technology,future" --target-length 60

# Run full pipeline with intelligent highlights
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video1/video1.mp4 --disable-viral-strategy

# Test mode commands (both --testmode and --testing-mode supported)
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video2/video.mp4 --disable-viral-strategy --testmode

# Production mode commands
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video2/video.mp4 --disable-viral-strategy

# With captions enabled
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video2/video.mp4 --disable-viral-strategy --enable-captions --caption-style youtube_shorts

# Testing mode with captions
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video1/video.mp4 --disable-viral-strategy --enable-captions --max-highlights 3 --min-duration 15

source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video2/video.mp4 --disable-viral-strategy --enable-captions --max-highlights 3 --min-duration 15

source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video3/video.mp4 --disable-viral-strategy --enable-captions --max-highlights 3 --min-duration 10
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video4/video.mp4 --disable-viral-strategy --enable-captions --max-highlights 3 --min-duration 10

# Testing mode with captions and viral strategy
source .venv/bin/activate && python tests/test_pipeline_sequential.py tests/sample/video2/video.mp4 --enable-captions --max-highlights 3


source .venv/bin/activate && python tests/test_pipeline_sequential.py --job-id "31b386ff-a477-4f6b-857b-336e62ffd098" --clip-number 4

