#!/usr/bin/env python3
"""
Integration tests for highlight processing with minimum duration enforcement

Tests that all highlight processing functions properly enforce the 10-second
minimum duration requirement across the entire pipeline.
"""

import unittest
import sys
import os
import tempfile
import json

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.video_highlights_detector import AdvancedHighlightsDetector
from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS


class TestHighlightProcessingIntegration(unittest.TestCase):
    """Integration tests for highlight processing with duration validation"""

    def setUp(self):
        """Set up test fixtures"""
        self.min_duration = MIN_HIGHLIGHT_DURATION_SECONDS
        self.detector = AdvancedHighlightsDetector()

        # Create sample transcript segments for testing
        self.sample_segments = [
            # Short segments (should be filtered out when combined)
            {'start': 0.0, 'end': 3.0, 'text': 'This is a short segment.'},
            {'start': 3.0, 'end': 6.0, 'text': 'Another short one.'},
            {'start': 6.0, 'end': 8.0, 'text': 'Very brief.'},

            # Medium segments (can form valid highlights when combined)
            {'start': 10.0, 'end': 15.0, 'text': 'This is an interesting question about life.'},
            {'start': 15.0, 'end': 20.0, 'text': 'The answer is quite profound and meaningful.'},
            {'start': 20.0, 'end': 25.0, 'text': 'It involves deep thinking and reflection.'},

            # Long segments (valid on their own)
            {'start': 30.0, 'end': 45.0, 'text': 'This is a long segment with lots of valuable content that discusses important topics and provides insights.'},
            {'start': 50.0, 'end': 65.0, 'text': 'Another substantial segment with meaningful dialogue and engaging conversation that would make a great highlight.'},

            # Edge case: exactly minimum duration
            {'start': 70.0, 'end': 80.0, 'text': 'This segment is exactly ten seconds long and should be valid.'},

            # Edge case: just under minimum duration
            {'start': 85.0, 'end': 94.9, 'text': 'This segment is just under ten seconds and should be filtered.'},
        ]

    def test_world_class_highlights_minimum_duration_enforcement(self):
        """Test that WorldClassHighlightsDetector enforces minimum duration"""
        highlights = self.detector.find_best_highlights(self.sample_segments)

        # Verify all highlights meet minimum duration requirement
        for highlight in highlights:
            duration = highlight.get('duration', 0)
            self.assertGreaterEqual(
                duration,
                self.min_duration,
                f"Highlight duration {duration:.1f}s is below minimum {self.min_duration}s"
            )

    def test_world_class_highlights_with_custom_minimum(self):
        """Test WorldClassHighlightsDetector with custom minimum duration"""
        custom_min = 15.0
        highlights = self.detector.find_best_highlights(
            self.sample_segments,
            min_duration_override=custom_min
        )

        # Verify all highlights meet custom minimum duration
        for highlight in highlights:
            duration = highlight.get('duration', 0)
            self.assertGreaterEqual(
                duration,
                custom_min,
                f"Highlight duration {duration:.1f}s is below custom minimum {custom_min}s"
            )

    def test_world_class_highlights_no_valid_highlights(self):
        """Test behavior when no segments meet minimum duration"""
        # Create segments that are all too short
        short_segments = [
            {'start': 0.0, 'end': 2.0, 'text': 'Short.'},
            {'start': 2.0, 'end': 4.0, 'text': 'Also short.'},
            {'start': 4.0, 'end': 6.0, 'text': 'Very short.'},
        ]

        highlights = self.detector.find_best_highlights(short_segments)

        # Should return empty list when no valid highlights can be created
        self.assertEqual(len(highlights), 0)

    def test_world_class_highlights_edge_cases(self):
        """Test edge cases for minimum duration enforcement"""
        # Test with exactly minimum duration
        exact_segments = [
            {'start': 0.0, 'end': 10.0, 'text': 'Exactly ten seconds of content here.'},
        ]

        highlights = self.detector.find_best_highlights(exact_segments)
        self.assertGreater(len(highlights), 0)
        self.assertEqual(highlights[0]['duration'], 10.0)

        # Test with just under minimum duration
        under_segments = [
            {'start': 0.0, 'end': 9.9, 'text': 'Just under ten seconds.'},
        ]

        highlights = self.detector.find_best_highlights(under_segments)
        self.assertEqual(len(highlights), 0)

    def test_world_class_highlights_logging_filtered_highlights(self):
        """Test that filtered highlights are properly logged"""
        # This test verifies that the logging functionality works
        # In a real scenario, you would capture log output to verify messages
        highlights = self.detector.find_best_highlights(self.sample_segments)

        # The test passes if no exceptions are raised during processing
        # and valid highlights are returned
        self.assertIsInstance(highlights, list)

        # Verify that only valid highlights are returned
        for highlight in highlights:
            self.assertGreaterEqual(highlight.get('duration', 0), self.min_duration)

    def test_highlight_metadata_preservation(self):
        """Test that highlight metadata is preserved during validation"""
        highlights = self.detector.find_best_highlights(self.sample_segments)

        for highlight in highlights:
            # Verify required fields are present
            self.assertIn('start_time', highlight)
            self.assertIn('end_time', highlight)
            self.assertIn('duration', highlight)
            self.assertIn('text', highlight)

            # Verify duration consistency
            calculated_duration = highlight['end_time'] - highlight['start_time']
            self.assertAlmostEqual(
                highlight['duration'],
                calculated_duration,
                places=1,
                msg="Duration field should match calculated duration"
            )

    def test_highlight_quality_with_duration_filter(self):
        """Test that duration filtering doesn't compromise highlight quality"""
        highlights = self.detector.find_best_highlights(self.sample_segments)

        if highlights:
            # Verify highlights are sorted by quality (score)
            scores = [h.get('engagement_score', 0) for h in highlights]
            self.assertEqual(scores, sorted(scores, reverse=True))

            # Verify all highlights have reasonable quality scores
            for highlight in highlights:
                score = highlight.get('engagement_score', 0)
                self.assertGreaterEqual(score, 0.0)
                self.assertLessEqual(score, 1.0)

    def test_multiple_minimum_duration_overrides(self):
        """Test multiple calls with different minimum duration overrides"""
        test_minimums = [5.0, 10.0, 15.0, 20.0]

        for min_duration in test_minimums:
            with self.subTest(min_duration=min_duration):
                highlights = self.detector.find_best_highlights(
                    self.sample_segments,
                    min_duration_override=min_duration
                )

                # Verify all highlights meet the specified minimum
                for highlight in highlights:
                    self.assertGreaterEqual(
                        highlight.get('duration', 0),
                        min_duration
                    )

    def test_highlight_processing_with_empty_segments(self):
        """Test highlight processing with empty or invalid segments"""
        # Empty segments list
        highlights = self.detector.find_best_highlights([])
        self.assertEqual(len(highlights), 0)

        # Segments with missing required fields
        invalid_segments = [
            {'start': 0.0, 'text': 'Missing end time'},
            {'end': 10.0, 'text': 'Missing start time'},
            {'start': 0.0, 'end': 10.0},  # Missing text
        ]

        # Should handle gracefully without crashing
        highlights = self.detector.find_best_highlights(invalid_segments)
        self.assertIsInstance(highlights, list)

    def test_highlight_duration_validation_consistency(self):
        """Test that duration validation is consistent across multiple runs"""
        # Run the same processing multiple times
        results = []
        for _ in range(3):
            highlights = self.detector.find_best_highlights(self.sample_segments)
            results.append(highlights)

        # Verify consistent results
        for i in range(1, len(results)):
            self.assertEqual(len(results[0]), len(results[i]))

            for j in range(len(results[0])):
                self.assertAlmostEqual(
                    results[0][j]['duration'],
                    results[i][j]['duration'],
                    places=1
                )

    def test_performance_with_duration_validation(self):
        """Test that duration validation doesn't significantly impact performance"""
        import time

        # Create a larger dataset for performance testing
        large_segments = []
        for i in range(100):
            large_segments.append({
                'start': i * 5.0,
                'end': (i + 1) * 5.0,
                'text': f'Segment {i} with some interesting content to analyze.'
            })

        start_time = time.time()
        highlights = self.detector.find_best_highlights(large_segments)
        end_time = time.time()

        processing_time = end_time - start_time

        # Verify processing completes in reasonable time (adjust threshold as needed)
        self.assertLess(processing_time, 10.0, "Processing should complete within 10 seconds")

        # Verify results are still valid
        for highlight in highlights:
            self.assertGreaterEqual(highlight.get('duration', 0), self.min_duration)


if __name__ == '__main__':
    unittest.main()
