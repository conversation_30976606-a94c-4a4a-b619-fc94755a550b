#!/usr/bin/env python3
"""
Video Highlights Detection System

Advanced highlight detection algorithm designed to find the most engaging,
valuable, and shareable moments in any video content.

Core Philosophy:
- Content quality over keyword matching
- Engagement potential over technical metrics
- Simplicity over complexity
- Universal applicability over niche optimization

Algorithm:
1. Content Analysis: Analyze transcript for engagement signals
2. Momentum Detection: Find moments of high information density
3. Emotional Peaks: Identify emotional highs and compelling moments
4. Natural Boundaries: Respect speech patterns and natural breaks
5. Optimal Selection: Choose best moments with diversity and flow
"""

import re
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)

# Import validation utilities for consistent minimum duration enforcement
try:
    from utils.validation_utils import HighlightValidationUtils
    from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS
except ImportError:
    # Fallback if imports fail
    HighlightValidationUtils = None
    MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

class WorldClassHighlightsDetector:
    """
    The world's best video highlights detection system.

    Finds the most engaging moments in any video by analyzing:
    - Speech patterns and energy
    - Information density and value
    - Emotional engagement signals
    - Natural conversation flow
    - Content uniqueness and impact
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Core parameters for finding ALL the best scenes
        self.min_highlight_duration = 10.0  # Minimum 10 seconds for impact
        self.max_highlight_duration = 30.0  # Maximum 30 seconds for attention span
        self.target_total_duration = 300.0  # Target 5 minutes of highlights (much more generous)
        self.overlap_threshold = 1.0        # Minimum gap between highlights (allow closer spacing)

        # Engagement signal patterns (the secret sauce)
        self.engagement_patterns = {
            # High-value question words that create curiosity
            'curiosity_triggers': [
                'what if', 'imagine', 'picture this', 'here\'s the thing',
                'the secret', 'the truth', 'nobody tells you', 'most people don\'t',
                'the real reason', 'what really happens', 'the problem is',
                'insight', 'we think', 'you can\'t', 'but we can', 'exactly',
                'i think', 'to add to that', 'the fastest way', 'in fact',
                'you know', 'the way that', 'they realize', 'the number one',
                'i heard', 'i like the story', 'where they', 'what they do'
            ],

            # Emotional intensity indicators
            'emotional_peaks': [
                'incredible', 'amazing', 'unbelievable', 'shocking', 'surprising',
                'devastating', 'heartbreaking', 'hilarious', 'terrifying',
                'mind-blowing', 'life-changing', 'game-changing'
            ],

            # Authority and credibility signals
            'authority_signals': [
                'research shows', 'studies prove', 'data reveals', 'experts say',
                'i discovered', 'i learned', 'i realized', 'the fact is',
                'what we found', 'the results show', 'absolutely', 'alanda barton says',
                'dale carnegie', 'michelle thomas', 'jeffrey miller', 'famous thread',
                'reddit', 'bore has famously wrote', 'my cousin said', 'taleb'
            ],

            # Story and narrative hooks
            'story_hooks': [
                'let me tell you', 'here\'s what happened', 'i remember when',
                'there was this time', 'picture this', 'imagine you\'re',
                'so there i was', 'it all started when'
            ],

            # Conflict and tension builders
            'tension_builders': [
                'but here\'s the problem', 'the issue is', 'what went wrong',
                'the mistake', 'the failure', 'the challenge', 'the struggle',
                'but then', 'suddenly', 'unexpectedly'
            ],

            # Resolution and insight moments
            'insight_moments': [
                'that\'s when i realized', 'the breakthrough came', 'it clicked',
                'suddenly it made sense', 'the answer was', 'i figured out',
                'the solution is', 'here\'s the key', 'change', 'relationship',
                'love', 'connection', 'unity', 'wholeness', 'decision', 'choice',
                'heuristic', 'path', 'painful', 'equanimous', 'peace', 'mental peace',
                'iterate', 'leverage', 'opportunities', 'friend circle', 'dating pool',
                'job opportunities', 'quality of life', 'genetics', 'behavior',
                'temperament', 'values', 'compromise', 'loss aversion', 'starting over',
                'successful people', 'mountain climbing', 'trauma', 'insight',
                'schedule', 'alienate', 'self-conscious', 'compliment', 'criticize',
                'confidence', 'passive observer', 'objective', 'threatened', 'fearful',
                'praiseworthy', 'authenticity', 'potential', 'resume', 'basis',
                'ineffable', 'consciousness', 'desire for unity', 'god-shaped hole',
                'mysticism', 'awe', 'sistine chapel', 'emotion', 'craves'
            ]
        }

        # Speech energy indicators
        self.energy_indicators = {
            'high_energy': ['!', 'really', 'absolutely', 'definitely', 'exactly', 'totally'],
            'emphasis': ['very', 'extremely', 'incredibly', 'massively', 'hugely'],
            'certainty': ['always', 'never', 'every', 'all', 'none', 'everything', 'nothing']
        }

        self.logger.info("World-Class Highlights Detector initialized")

    def find_best_highlights(self, transcript_segments: List[Dict[str, Any]],
                           video_duration: Optional[float] = None,
                           max_highlights: Optional[int] = None,
                           min_duration_override: Optional[float] = None,
                           max_duration_override: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Find the absolute best highlights in the video.

        Args:
            transcript_segments: List of transcript segments with timing
            video_duration: Total video duration (optional)
            max_highlights: Maximum number of highlights to generate (optional, defaults to 100 for production, 1 for testing)
            min_duration_override: Optional override for minimum highlight duration.
            max_duration_override: Optional override for maximum highlight duration.

        Returns:
            List of the best highlight segments with scores and metadata
        """
        if not transcript_segments:
            return []

        # Store original durations and apply overrides if provided
        original_min_highlight_duration = self.min_highlight_duration
        original_max_highlight_duration = self.max_highlight_duration

        if min_duration_override is not None:
            self.min_highlight_duration = float(min_duration_override)
            self.logger.info(f"Overriding min_highlight_duration to: {self.min_highlight_duration}s")
        if max_duration_override is not None:
            self.max_highlight_duration = float(max_duration_override)
            self.logger.info(f"Overriding max_highlight_duration to: {self.max_highlight_duration}s")

        # Ensure min_highlight_duration is used consistently
        self.logger.info(f"Using min_highlight_duration: {self.min_highlight_duration}s for this run.")

        if max_highlights is not None:
            if max_highlights == 1:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments to find the single best highlight")
            elif max_highlights <= 3:
                self.logger.info(f"🧪 Testing mode: Analyzing {len(transcript_segments)} segments for up to {max_highlights} highlights")
            else:
                self.logger.info(f"Analyzing {len(transcript_segments)} segments for world-class highlights (max: {max_highlights})")
        else:
            self.logger.info(f"Analyzing {len(transcript_segments)} segments for world-class highlights")

        # Step 1: Analyze each segment for engagement potential
        segment_scores = self._analyze_segment_engagement(transcript_segments)

        # Step 2: Find momentum peaks (high information density moments)
        momentum_peaks = self._find_momentum_peaks(transcript_segments, segment_scores)

        # Step 3: Detect emotional and narrative peaks
        narrative_peaks = self._find_narrative_peaks(transcript_segments)

        # Step 4: Generate candidate highlight windows
        candidates = self._generate_highlight_candidates(
            transcript_segments, segment_scores, momentum_peaks, narrative_peaks
        )

        # Step 5: Score and rank all candidates
        scored_candidates = self._score_highlight_candidates(candidates, transcript_segments)

        # Step 6: Select optimal set of non-overlapping highlights
        final_highlights = self._select_optimal_highlights(scored_candidates, max_highlights)

        self.logger.info(f"Selected {len(final_highlights)} world-class highlights "
                        f"({sum(h['duration'] for h in final_highlights):.1f}s total)")

        # Restore original durations
        self.min_highlight_duration = original_min_highlight_duration
        self.max_highlight_duration = original_max_highlight_duration

        return final_highlights

    def _analyze_segment_engagement(self, segments: List[Dict[str, Any]]) -> List[float]:
        """Analyze each segment for engagement potential."""
        scores = []

        for segment in segments:
            text = segment.get('text', '').lower()
            score = 0.0

            # Check for engagement patterns
            for category, patterns in self.engagement_patterns.items():
                for pattern in patterns:
                    if pattern in text:
                        # Weight different categories differently
                        if category == 'curiosity_triggers':
                            score += 0.8
                        elif category == 'emotional_peaks':
                            score += 0.7
                        elif category == 'authority_signals':
                            score += 0.6
                        elif category == 'story_hooks':
                            score += 0.5
                        elif category == 'tension_builders':
                            score += 0.6
                        elif category == 'insight_moments':
                            score += 0.7

            # Check for energy indicators
            for category, indicators in self.energy_indicators.items():
                for indicator in indicators:
                    if indicator in text:
                        if category == 'high_energy':
                            score += 0.3
                        elif category == 'emphasis':
                            score += 0.2
                        elif category == 'certainty':
                            score += 0.2

            # Bonus for questions (natural engagement)
            if '?' in segment.get('text', ''):
                score += 0.4

            # Bonus for longer segments (more content)
            duration = segment.get('end', 0) - segment.get('start', 0)
            if duration > 3.0:
                score += 0.2

            # Normalize score
            score = min(1.0, score)
            scores.append(score)

        return scores

    def _find_momentum_peaks(self, segments: List[Dict[str, Any]],
                           scores: List[float]) -> List[int]:
        """Find moments of high information momentum."""
        if len(scores) < 3:
            return []

        peaks = []
        window_size = 3  # Look at 3-segment windows

        for i in range(window_size, len(scores) - window_size):
            # Calculate momentum as rate of score increase
            current_window = scores[i-window_size:i+window_size+1]
            avg_score = statistics.mean(current_window)

            # Much more sensitive peak detection to find ALL good moments
            if (avg_score > 0.2 and  # Lower threshold to catch more peaks
                scores[i] >= max(scores[i-1], scores[i+1]) and
                avg_score > statistics.mean(scores[max(0, i-10):i+10]) * 0.8):  # More lenient comparison
                peaks.append(i)

        return peaks

    def _find_narrative_peaks(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find narrative and emotional peaks in the content."""
        peaks = []

        for i, segment in enumerate(segments):
            text = segment.get('text', '')

            # Look for story structures and emotional moments
            peak_score = 0.0
            peak_type = 'general'

            # Check for story climax patterns
            climax_patterns = [
                'and then', 'suddenly', 'all of a sudden', 'that\'s when',
                'the moment', 'right then', 'at that point', 'it was then'
            ]

            for pattern in climax_patterns:
                if pattern in text.lower():
                    peak_score += 0.6
                    peak_type = 'climax'
                    break

            # Check for revelation patterns
            revelation_patterns = [
                'i realized', 'it hit me', 'i understood', 'it became clear',
                'the truth was', 'what i discovered', 'i found out'
            ]

            for pattern in revelation_patterns:
                if pattern in text.lower():
                    peak_score += 0.7
                    peak_type = 'revelation'
                    break

            # Check for emotional intensity
            if any(word in text.lower() for word in self.engagement_patterns['emotional_peaks']):
                peak_score += 0.5
                if peak_type == 'general':
                    peak_type = 'emotional'

            if peak_score > 0.2:  # Much lower threshold to catch more narrative moments
                peaks.append({
                    'segment_idx': i,
                    'score': peak_score,
                    'type': peak_type,
                    'text': text
                })

        return peaks

    def _generate_highlight_candidates(self, segments: List[Dict[str, Any]],
                                     scores: List[float], momentum_peaks: List[int],
                                     narrative_peaks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate candidate highlight windows around peaks."""
        candidates = []

        # Generate candidates around momentum peaks
        for peak_idx in momentum_peaks:
            candidates.extend(self._create_windows_around_peak(segments, peak_idx, 'momentum'))

        # Generate candidates around narrative peaks
        for peak in narrative_peaks:
            peak_idx = peak['segment_idx']
            candidates.extend(self._create_windows_around_peak(segments, peak_idx, peak['type']))

        # Generate candidates around high-scoring segments (much more inclusive)
        for i, score in enumerate(scores):
            if score > 0.3:  # Lower threshold to catch more engaging moments
                candidates.extend(self._create_windows_around_peak(segments, i, 'engagement'))

        # Remove duplicates and merge overlapping candidates
        candidates = self._merge_overlapping_candidates(candidates)

        # Ensure minimum duration in highlight generation
        candidates = [c for c in candidates if c.get('duration', 0) >= self.min_highlight_duration]

        return candidates

    def _create_windows_around_peak(self, segments: List[Dict[str, Any]],
                                  peak_idx: int, peak_type: str) -> List[Dict[str, Any]]:
        """Create highlight windows of different sizes around a peak."""
        windows = []

        # Create windows of different sizes (all at least min_highlight_duration) to catch more content
        duration_targets = [max(d, self.min_highlight_duration) for d in [10, 12, 15, 18, 20, 25, 30]]
        for duration_target in duration_targets:
            # Try centering the peak in the window
            window = self._create_window_centered_on_peak(segments, peak_idx, duration_target)
            if window:
                window['peak_type'] = peak_type
                window['peak_idx'] = peak_idx
                windows.append(window)

        return windows

    def _create_window_centered_on_peak(self, segments: List[Dict[str, Any]],
                                      peak_idx: int, target_duration: float) -> Optional[Dict[str, Any]]:
        """Create a window of target duration centered on the peak."""
        if peak_idx >= len(segments):
            return None

        peak_start = segments[peak_idx].get('start', 0)
        peak_end = segments[peak_idx].get('end', 0)

        # Start with the peak segment
        window_start_idx = peak_idx
        window_end_idx = peak_idx
        current_duration = peak_end - peak_start

        # Expand window to reach minimum target duration
        target_duration = max(target_duration, self.min_highlight_duration)  # Ensure we meet minimum duration
        while current_duration < target_duration:
            expanded = False

            # Try expanding backward
            if window_start_idx > 0:
                new_start = segments[window_start_idx - 1].get('start', 0)
                new_duration = segments[window_end_idx].get('end', 0) - new_start
                if new_duration <= target_duration + 2.0:  # Allow 2s tolerance
                    window_start_idx -= 1
                    current_duration = new_duration
                    expanded = True

            # Try expanding forward
            if window_end_idx < len(segments) - 1 and not expanded:
                new_end = segments[window_end_idx + 1].get('end', 0)
                new_duration = new_end - segments[window_start_idx].get('start', 0)
                if new_duration <= target_duration + 2.0:  # Allow 2s tolerance
                    window_end_idx += 1
                    current_duration = new_duration
                    expanded = True

            if not expanded:
                break

        # Only return if we have a reasonable duration
        if current_duration >= self.min_highlight_duration:
            window_text = ' '.join(seg.get('text', '') for seg in segments[window_start_idx:window_end_idx+1])

            return {
                'start_time': segments[window_start_idx].get('start', 0),
                'end_time': segments[window_end_idx].get('end', 0),
                'duration': current_duration,
                'start_idx': window_start_idx,
                'end_idx': window_end_idx,
                'text': window_text,
                'peak_segment_idx': peak_idx
            }

        return None

    def _merge_overlapping_candidates(self, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge overlapping candidate windows."""
        if not candidates:
            return []

        # Sort by start time
        candidates.sort(key=lambda x: x['start_time'])

        merged = []
        current = candidates[0]

        for next_candidate in candidates[1:]:
            # Check for overlap (with small tolerance)
            if next_candidate['start_time'] <= current['end_time'] + 1.0:
                # Merge candidates - keep the one with better peak type priority
                peak_priority = {'revelation': 4, 'climax': 3, 'emotional': 2, 'momentum': 1, 'engagement': 0}

                current_priority = peak_priority.get(current.get('peak_type', 'engagement'), 0)
                next_priority = peak_priority.get(next_candidate.get('peak_type', 'engagement'), 0)

                if next_priority > current_priority:
                    current = next_candidate
                # If same priority, keep the longer one
                elif next_priority == current_priority and next_candidate['duration'] > current['duration']:
                    current = next_candidate
            else:
                merged.append(current)
                current = next_candidate

        merged.append(current)
        return merged

    def _score_highlight_candidates(self, candidates: List[Dict[str, Any]],
                                  segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Score all highlight candidates using world-class metrics."""
        scored_candidates = []

        for candidate in candidates:
            score = self._calculate_world_class_score(candidate, segments)
            candidate['world_class_score'] = score
            candidate['score_per_second'] = score / candidate['duration']
            scored_candidates.append(candidate)

        # Sort by score per second (efficiency metric)
        scored_candidates.sort(key=lambda x: x['score_per_second'], reverse=True)

        return scored_candidates

    def _calculate_world_class_score(self, candidate: Dict[str, Any],
                                   segments: List[Dict[str, Any]]) -> float:
        """Calculate the world-class engagement score for a candidate."""
        text = candidate.get('text', '').lower()
        duration = candidate.get('duration', 0)
        peak_type = candidate.get('peak_type', 'general')

        score = 0.0

        # 1. Peak Type Bonus (30% of score)
        peak_bonuses = {
            'revelation': 0.9,    # Highest value - insights and realizations
            'climax': 0.8,        # Story peaks and dramatic moments
            'emotional': 0.7,     # Emotional intensity
            'momentum': 0.6,      # Information density peaks
            'engagement': 0.5     # General engagement signals
        }
        score += peak_bonuses.get(peak_type, 0.3) * 0.3

        # 2. Content Quality Analysis (40% of score)
        content_score = 0.0

        # Check for high-value content patterns
        if any(pattern in text for pattern in self.engagement_patterns['curiosity_triggers']):
            content_score += 0.4
        if any(pattern in text for pattern in self.engagement_patterns['authority_signals']):
            content_score += 0.3
        if any(pattern in text for pattern in self.engagement_patterns['insight_moments']):
            content_score += 0.4
        if any(pattern in text for pattern in self.engagement_patterns['story_hooks']):
            content_score += 0.2

        # Bonus for questions (natural engagement)
        question_count = text.count('?')
        content_score += min(0.3, question_count * 0.1)

        # Information density (words per second)
        word_count = len(text.split())
        words_per_second = word_count / duration if duration > 0 else 0
        if 2.0 <= words_per_second <= 4.0:  # Optimal speaking pace
            content_score += 0.2
        elif words_per_second > 4.0:  # High information density
            content_score += 0.3

        score += min(1.0, content_score) * 0.4

        # 3. Duration Optimization (20% of score)
        duration_score = 0.0
        if 12 <= duration <= 18:  # Sweet spot for social media
            duration_score = 1.0
        elif 8 <= duration <= 25:  # Acceptable range
            duration_score = 0.8
        else:
            duration_score = 0.4

        score += duration_score * 0.2

        # 4. Uniqueness Bonus (10% of score)
        # Simple uniqueness based on rare words
        words = text.split()
        unique_words = len(set(words))
        uniqueness = unique_words / len(words) if words else 0
        score += uniqueness * 0.1

        return min(1.0, score)

    def _select_optimal_highlights(self, candidates: List[Dict[str, Any]],
                                 max_highlights: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Select the optimal set of non-overlapping highlights.

        This method implements a greedy selection algorithm that prioritizes the highest-scoring
        candidates while ensuring no temporal overlap between selected highlights.

        Args:
            candidates: List of scored highlight candidates sorted by quality
            max_highlights: Maximum number of highlights to select (optional, defaults to 100 for production)

        Returns:
            List of selected highlights with metadata
        """
        if not candidates:
            self.logger.warning("No highlight candidates provided for selection")
            return []

        # Filter out candidates that are too short before selection using validation utilities
        if HighlightValidationUtils:
            # Use the validation utilities for consistent enforcement
            candidates = HighlightValidationUtils.enforce_minimum_duration(
                candidates,
                min_duration=self.min_highlight_duration,
                log_filtered=True
            )
        else:
            # Fallback to simple filtering
            candidates = [c for c in candidates if c.get('duration', 0) >= self.min_highlight_duration]

        if not candidates:
            self.logger.warning(f"No candidates meet minimum duration requirement ({self.min_highlight_duration}s)")
            return []

        selected = []
        total_duration = 0.0

        # Determine the maximum number of highlights to select
        max_count = max_highlights if max_highlights is not None else 100

        # Validate max_highlights parameter
        if max_count < 1:
            self.logger.warning(f"Invalid max_highlights value: {max_count}. Using default of 1.")
            max_count = 1

        if max_highlights is not None:
            if max_highlights == 1:
                self.logger.info(f"🧪 Testing mode: Selecting only the highest-scoring highlight for fast testing")
            elif max_highlights <= 3:
                self.logger.info(f"🧪 Testing mode: Selecting up to {max_count} highlights for fast testing")
            else:
                self.logger.info(f"🎯 Selecting up to {max_count} highlights (custom limit)")
        else:
            self.logger.info(f"🎯 Selecting up to {max_count} highlights (production mode)")

        self.logger.info(f"Selecting optimal highlights from {len(candidates)} candidates")
        self.logger.info(f"Maximum highlights allowed: {max_count}")
        self.logger.info(f"Target total duration: {self.target_total_duration}s")

        # Greedy selection of best non-overlapping candidates
        for i, candidate in enumerate(candidates):
            try:
                # Check if this candidate meets the minimum duration requirement
                if candidate.get('duration', 0) < self.min_highlight_duration:
                    continue

                # Check if this candidate overlaps with any selected highlight
                if not self._has_overlap_with_selected(candidate, selected):
                    # Check if adding this would exceed our target duration
                    if total_duration + candidate['duration'] <= self.target_total_duration:
                        selected.append(candidate)
                        total_duration += candidate['duration']

                        self.logger.info(f"Selected highlight {len(selected)}: "
                                       f"{candidate['duration']:.1f}s "
                                       f"(score: {candidate.get('world_class_score', 0):.3f})")

                        # Stop when we reach the maximum count or target duration
                        if len(selected) >= max_count or total_duration >= self.target_total_duration * 0.95:
                            self.logger.info(f"Reached maximum highlights limit ({max_count}) or duration target")
                            break
                else:
                    self.logger.debug(f"Candidate {i+1} overlaps with existing selection, skipping")

            except Exception as e:
                self.logger.error(f"Error processing candidate {i+1}: {str(e)}")
                continue

        # Sort selected highlights by start time for chronological order
        selected.sort(key=lambda x: x['start_time'])

        # Add final metadata with improved error handling
        for i, highlight in enumerate(selected):
            try:
                highlight['highlight_index'] = i + 1
                highlight['total_highlights'] = len(selected)
                highlight['selection_method'] = 'world_class_greedy'
                highlight['selection_timestamp'] = time.time()
            except Exception as e:
                self.logger.error(f"Error adding metadata to highlight {i+1}: {str(e)}")

        self.logger.info(f"Final selection: {len(selected)} highlights, "
                        f"total duration: {total_duration:.1f}s")

        return selected

    def _has_overlap_with_selected(self, candidate: Dict[str, Any],
                                 selected: List[Dict[str, Any]]) -> bool:
        """Check if candidate overlaps with any selected highlight."""
        candidate_start = candidate['start_time']
        candidate_end = candidate['end_time']

        for selected_highlight in selected:
            selected_start = selected_highlight['start_time']
            selected_end = selected_highlight['end_time']

            # Check for overlap with minimum gap requirement
            if not (candidate_end + self.overlap_threshold <= selected_start or
                   candidate_start >= selected_end + self.overlap_threshold):
                return True

        return False


# Create global instance for easy access
world_class_detector = WorldClassHighlightsDetector()